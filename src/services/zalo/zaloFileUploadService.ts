// ZALO File Upload Service
// Handles file and image uploads to ZALO OA API with domain isolation

import { ZaloURL } from '@/types/social/zaloTypes'
import type {
  ZaloOaUploadResponse
} from '@/types/apps/zalo-oa/zaloOaTypes'
import { ZaloTokenService } from './zaloTokenService'

export class ZaloFileUploadService {
  /**
   * Upload image to ZALO OA API
   * @param domainUuid - Domain UUID for token management
   * @param file - File buffer or File object
   * @param filename - Original filename
   * @param contentType - MIME type of the file
   * @returns Upload response with attachment_id
   */
  static async uploadImage(
    domainUuid: string,
    file: Buffer | File,
    filename: string,
    contentType: string
  ): Promise<ZaloOaUploadResponse> {
    console.log('=== ZALO IMAGE UPLOAD START ===')
    console.log('Parameters:', {
      domainUuid,
      filename,
      contentType,
      fileSize: file instanceof Buffer ? file.length : file.size
    })

    try {
      // Validate image type
      if (!contentType.startsWith('image/')) {
        throw new Error('File must be an image')
      }

      // Supported image formats by Zalo
      const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']

      if (!supportedFormats.includes(contentType.toLowerCase())) {
        throw new Error(`Unsupported image format: ${contentType}. Supported: ${supportedFormats.join(', ')}`)
      }

      // File size limit (10MB for images)
      const maxSize = 10 * 1024 * 1024 // 10MB
      const fileSize = file instanceof Buffer ? file.length : file.size

      if (fileSize > maxSize) {
        throw new Error(`Image too large: ${fileSize} bytes. Maximum: ${maxSize} bytes`)
      }

      // Create FormData
      const formData = new FormData()
      
      if (file instanceof Buffer) {
        const blob = new Blob([file], { type: contentType })

        formData.append('file', blob, filename)
      } else {
        formData.append('file', file, filename)
      }

      console.log('Uploading image to Zalo API...')

      // Use token service to make API call with automatic refresh
      const response = await ZaloTokenService.makeApiCallWithTokenRefresh(
        domainUuid,
        async (accessToken: string) => {
          console.log('Making HTTP request to Zalo image upload API...')

          const res = await fetch(ZaloURL.ZALO_UPLOAD_IMAGE_URL, {
            method: 'POST',
            headers: {
              'access_token': accessToken

              // Don't set Content-Type header - let browser set it with boundary for FormData
            },
            body: formData
          })

          if (!res.ok) {
            const errorText = await res.text()

            console.error('Zalo image upload HTTP error:', {
              status: res.status,
              statusText: res.statusText,
              body: errorText
            })
            throw new Error(`HTTP ${res.status}: ${res.statusText}`)
          }

          const result = await res.json()

          console.log('Zalo image upload API response:', result)

          return result
        }
      )

      if (response.error !== 0) {
        console.error('Zalo image upload API error:', response)
        
return {
          error: response.error,
          message: response.message || 'Image upload failed'
        }
      }

      console.log('Image upload successful:', {
        attachment_id: response.data?.attachment_id,
        url: response.data?.url
      })

      return {
        error: 0,
        message: 'Image uploaded successfully',
        data: response.data
      }

    } catch (error) {
      console.error('Error uploading image to Zalo:', error)
      
return {
        error: -1,
        message: error instanceof Error ? error.message : 'Unknown upload error'
      }
    }
  }

  /**
   * Upload file to ZALO OA API
   * @param domainUuid - Domain UUID for token management
   * @param file - File buffer or File object
   * @param filename - Original filename
   * @param contentType - MIME type of the file
   * @returns Upload response with attachment_id
   */
  static async uploadFile(
    domainUuid: string,
    file: Buffer | File,
    filename: string,
    contentType: string
  ): Promise<ZaloOaUploadResponse> {
    console.log('=== ZALO FILE UPLOAD START ===')
    console.log('Parameters:', {
      domainUuid,
      filename,
      contentType,
      fileSize: file instanceof Buffer ? file.length : file.size
    })

    try {
      // Validate file type - Zalo supports various file types
      const supportedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/plain',
        'application/zip',
        'application/x-zip-compressed'
      ]

      if (!supportedTypes.includes(contentType.toLowerCase())) {
        throw new Error(`Unsupported file format: ${contentType}. Supported: ${supportedTypes.join(', ')}`)
      }

      // File size limit (25MB for files)
      const maxSize = 25 * 1024 * 1024 // 25MB
      const fileSize = file instanceof Buffer ? file.length : file.size

      if (fileSize > maxSize) {
        throw new Error(`File too large: ${fileSize} bytes. Maximum: ${maxSize} bytes`)
      }

      // Create FormData
      const formData = new FormData()
      
      if (file instanceof Buffer) {
        const blob = new Blob([file], { type: contentType })

        formData.append('file', blob, filename)
      } else {
        formData.append('file', file, filename)
      }

      console.log('Uploading file to Zalo API...')

      // Use token service to make API call with automatic refresh
      const response = await ZaloTokenService.makeApiCallWithTokenRefresh(
        domainUuid,
        async (accessToken: string) => {
          console.log('Making HTTP request to Zalo file upload API...')

          const res = await fetch(ZaloURL.ZALO_UPLOAD_FILE_URL, {
            method: 'POST',
            headers: {
              'access_token': accessToken

              // Don't set Content-Type header - let browser set it with boundary for FormData
            },
            body: formData
          })

          if (!res.ok) {
            const errorText = await res.text()

            console.error('Zalo file upload HTTP error:', {
              status: res.status,
              statusText: res.statusText,
              body: errorText
            })
            throw new Error(`HTTP ${res.status}: ${res.statusText}`)
          }

          const result = await res.json()

          console.log('Zalo file upload API response:', result)

          return result
        }
      )

      if (response.error !== 0) {
        console.error('Zalo file upload API error:', response)
        
return {
          error: response.error,
          message: response.message || 'File upload failed'
        }
      }

      console.log('File upload successful:', {
        attachment_id: response.data?.attachment_id
      })

      return {
        error: 0,
        message: 'File uploaded successfully',
        data: response.data
      }

    } catch (error) {
      console.error('Error uploading file to Zalo:', error)
      
return {
        error: -1,
        message: error instanceof Error ? error.message : 'Unknown upload error'
      }
    }
  }

  /**
   * Determine upload method based on content type
   * @param contentType - MIME type of the file
   * @returns 'image' or 'file'
   */
  static getUploadType(contentType: string): 'image' | 'file' {
    if (contentType.startsWith('image/')) {
      return 'image'
    }

    
return 'file'
  }

  /**
   * Upload file with automatic type detection
   * @param domainUuid - Domain UUID for token management
   * @param file - File buffer or File object
   * @param filename - Original filename
   * @param contentType - MIME type of the file
   * @returns Upload response with attachment_id
   */
  static async uploadAuto(
    domainUuid: string,
    file: Buffer | File,
    filename: string,
    contentType: string
  ): Promise<ZaloOaUploadResponse> {
    const uploadType = this.getUploadType(contentType)
    
    if (uploadType === 'image') {
      return this.uploadImage(domainUuid, file, filename, contentType)
    } else {
      return this.uploadFile(domainUuid, file, filename, contentType)
    }
  }

  /**
   * Validate file for Zalo upload
   * @param file - File to validate
   * @param contentType - MIME type
   * @returns Validation result
   */
  static validateFile(file: Buffer | File, contentType: string): {
    valid: boolean
    error?: string
    uploadType?: 'image' | 'file'
  } {
    try {
      const fileSize = file instanceof Buffer ? file.length : file.size
      const uploadType = this.getUploadType(contentType)

      // Check file size limits
      if (uploadType === 'image') {
        const maxSize = 10 * 1024 * 1024 // 10MB for images

        if (fileSize > maxSize) {
          return {
            valid: false,
            error: `Image too large: ${fileSize} bytes. Maximum: ${maxSize} bytes`
          }
        }

        const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']

        if (!supportedFormats.includes(contentType.toLowerCase())) {
          return {
            valid: false,
            error: `Unsupported image format: ${contentType}`
          }
        }
      } else {
        const maxSize = 25 * 1024 * 1024 // 25MB for files

        if (fileSize > maxSize) {
          return {
            valid: false,
            error: `File too large: ${fileSize} bytes. Maximum: ${maxSize} bytes`
          }
        }

        const supportedTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-powerpoint',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          'text/plain',
          'application/zip',
          'application/x-zip-compressed'
        ]

        if (!supportedTypes.includes(contentType.toLowerCase())) {
          return {
            valid: false,
            error: `Unsupported file format: ${contentType}`
          }
        }
      }

      return {
        valid: true,
        uploadType
      }
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Validation error'
      }
    }
  }
}
