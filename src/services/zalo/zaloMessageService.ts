// Enhanced ZALO Message Service
// Handles inbound/outbound message processing following clean code guidelines

import crypto from 'crypto'

import { prisma } from '@/libs/db/prisma'
import { ZaloTokenService } from './zaloTokenService'
import { ZaloFileUploadService } from './zaloFileUploadService'
import type {
  ZaloWebhookPayload,
  ZaloContact,
  ZaloChatRoom,
  ZaloProcessedMessage,
  ZaloUser
} from '@/types/social/zaloTypes'
import { ZaloEventType, ZaloURL } from '@/types/social/zaloTypes'
import { MessageType } from '@/types/apps/internal-chat/chatTypes'
import { mapMessageType } from '@/utils/social/platformUtils'
import {
  createInternalChatRoom,
  createInternalMessage,
  updateRoomLastActivity,
  createMessageNotification,
  validateMessageContent,
  safeBigIntToString
} from '@/utils/social/messageUtils'

export class ZaloMessageService {
  /**
   * Verify ZALO webhook signature for security
   * Follows <PERSON><PERSON>'s signature verification algorithm:
   * 1. Sort all fields alphabetically
   * 2. Concatenate values in sorted order
   * 3. Generate SHA256 hash with webhook_secret or app_secret
   */
  static async verifyWebhookSignature(domainUuid: string, payload: string, signature: string | null): Promise<boolean> {
    if (!signature) {
      console.error('No signature provided for ZALO webhook')

      return false
    }

    try {
      // Get ZALO configuration for this domain
      const config = await prisma.v_zalo_oa_config.findFirst({
        where: {
          domain_uuid: domainUuid,
          is_active: true
        },
        select: {
          webhook_secret: true,
          app_secret: true
        }
      })

      if (!config) {
        console.error('ZALO configuration not found for domain:', domainUuid)

        return false
      }

      // Use webhook_secret if available, fallback to app_secret
      const secret = config.webhook_secret || config.app_secret

      if (!secret) {
        console.error('No webhook secret or app secret found for ZALO configuration')

        return false
      }

      // Parse webhook payload
      const data = JSON.parse(payload)

      // Generate signature following Zalo's algorithm: sha256(content + apiKey)
      const expectedSignature = this.generateZaloSignature(data, secret)

      console.log('=== SIGNATURE VERIFICATION DEBUG ===')
      console.log('Raw signature from header:', signature)
      console.log('Expected signature:', expectedSignature)

      // Clean and normalize the provided signature
      let cleanSignature = signature.trim()

      // Remove any prefix if present (ZALO uses 'mac=' prefix)
      if (cleanSignature.startsWith('mac=')) {
        cleanSignature = cleanSignature.substring(4)
      } else if (cleanSignature.startsWith('sha256=')) {
        cleanSignature = cleanSignature.substring(7)
      }

      console.log('Cleaned signature:', cleanSignature)

      // Validate signature format and length
      if (!/^[a-fA-F0-9]+$/.test(cleanSignature)) {
        console.error('Invalid signature format - not hexadecimal:', cleanSignature)

        return false
      }

      // Ensure both signatures have the same length before comparison
      if (expectedSignature.length !== cleanSignature.length) {
        console.error('Signature length mismatch:', {
          expected_length: expectedSignature.length,
          provided_length: cleanSignature.length,
          expected: expectedSignature,
          provided: cleanSignature
        })

        return false
      }

      // Compare signatures using timing-safe comparison
      const expectedBuffer = Buffer.from(expectedSignature, 'hex')
      const providedBuffer = Buffer.from(cleanSignature, 'hex')

      return crypto.timingSafeEqual(
        new Uint8Array(expectedBuffer),
        new Uint8Array(providedBuffer)
      )
    } catch (error) {
      console.error('ZALO webhook signature verification failed:', error)

      return false
    }
  }

  /**
   * Generate ZALO signature following their official algorithm
   * Based on ZALO Mini App documentation: sha256(content + apiKey)
   * Where content = concatenated values of all fields sorted alphabetically
   * @param data - Parsed webhook payload object
   * @param secret - Webhook secret or app secret (apiKey)
   */
  private static generateZaloSignature(data: any, secret: string): string {
    try {
      console.log('=== SIGNATURE GENERATION DEBUG ===')
      console.log('Secret length:', secret.length)

      // Get all field names and sort alphabetically (A-Z)
      const keys = Object.keys(data).sort()

      console.log('Data keys (sorted):', keys)

      // Build content string by concatenating values in sorted order
      let content = ''

      for (const key of keys) {
        let value = data[key]

        // Convert objects to JSON strings
        if (typeof value === 'object' && value !== null) {
          value = JSON.stringify(value)
        }

        content += value
        console.log(`Key: ${key}, Value: ${value}`)
      }

      console.log('Final content string:', content)
      console.log('Content + secret:', `${content}${secret}`)

      // Generate SHA256 hash: sha256(content + secret)
      const signature = crypto
        .createHash('sha256')
        .update(`${content}${secret}`)
        .digest('hex')

      console.log('Generated signature:', signature)
      console.log('================================')

      return signature
    } catch (error) {
      console.error('Error generating ZALO signature:', error)
      throw error
    }
  }

  /**
   * Process inbound ZALO message and create internal chat message
   */
  static async processInboundMessage(
    domainUuid: string,
    webhookEvent: ZaloWebhookPayload
  ): Promise<ZaloProcessedMessage> {
    console.log('=== ZALO MESSAGE PROCESSING START ===')
    console.log('Domain:', domainUuid)
    console.log('Event Name:', webhookEvent.event_name)
    console.log('Sender ID:', webhookEvent.sender.user_id)
    console.log('Message:', webhookEvent.message?.text || '[no text]')

    try {
      // Validate message content
      if (!webhookEvent.message?.text && !webhookEvent.message?.attachments) {
        throw new Error('No message content or attachments')
      }

      // Find or create ZALO contact
      console.log('Step 1: Finding or creating contact...')
      const contact = await this.findOrCreateContact(domainUuid, webhookEvent.sender)

      console.log('Contact created/found:', contact.contact_uuid, contact.display_name)

      // Find or create room bridge
      console.log('Step 2: Finding or creating room bridge...')
      const zaloRoom = await this.findOrCreateRoom(domainUuid, contact, webhookEvent.sender.user_id)

      console.log('Room created/found:', zaloRoom.zalo_room_uuid, zaloRoom.internal_room_uuid)

      // Create internal message
      console.log('Step 3: Creating internal message...')
      const messageContent = webhookEvent.message.text || '[Attachment]'

      const messageType = mapMessageType(this.getMessageTypeFromEvent(webhookEvent.event_name), 'zalo')

      const internalMessage = await createInternalMessage(
        zaloRoom.internal_room_uuid,
        null, // External messages don't have internal author
        messageContent,
        messageType,
        undefined,
        4 // External message flag
      )

      console.log('Internal message created:', internalMessage.message_id.toString())

      // Create message mapping
      console.log('Step 4: Creating message mapping...')
      await this.createMessageMapping(domainUuid, internalMessage.message_id, webhookEvent, 'inbound')
      console.log('Message mapping created successfully')

      // Update room last activity
      await updateRoomLastActivity(zaloRoom.internal_room_uuid)

      // Create notification
      await createMessageNotification(zaloRoom.internal_room_uuid, internalMessage.message_id)

      const result = {
        internal_message_id: safeBigIntToString(internalMessage.message_id) || '0',
        room_uuid: zaloRoom.internal_room_uuid,
        contact_uuid: contact.contact_uuid,
        zalo_message_id: webhookEvent.message.msg_id,
        delivery_status: 'delivered'
      }

      console.log('=== ZALO MESSAGE PROCESSING COMPLETE ===')
      console.log('Result:', result)
      console.log('=========================================')

      return result
    } catch (error) {
      console.error('=== ZALO MESSAGE PROCESSING ERROR ===')
      console.error('Error processing ZALO message:', error)
      console.error('Domain UUID:', domainUuid)
      console.error('Sender ID:', webhookEvent.sender.user_id)
      throw error
    }
  }

  /**
   * Process outbound message from internal chat to ZALO
   */
  static async processOutboundMessage(
    domainUuid: string,
    roomUuid: string,
    authorUuid: string,
    content: string
  ): Promise<ZaloProcessedMessage> {
    console.log('=== ZALO MESSAGE SERVICE: PROCESS OUTBOUND MESSAGE ===')
    console.log('Parameters:', {
      domainUuid,
      roomUuid,
      authorUuid,
      content: content.substring(0, 50) + (content.length > 50 ? '...' : '')
    })

    try {
      // Validate message content
      const validation = validateMessageContent(content)

      if (!validation.valid) {
        console.error('Message content validation failed:', validation.error)
        throw new Error(validation.error)
      }

      console.log('Message content validation passed')

      // Find ZALO room bridge
      console.log('Looking for Zalo room bridge...')

      const zaloRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
        where: {
          domain_uuid: domainUuid,
          internal_room_uuid: roomUuid
        },
        include: {
          v_zalo_oa_contacts: true
        }
      })

      if (!zaloRoom) {
        console.error('ZALO room bridge not found:', { domainUuid, roomUuid })
        throw new Error('ZALO room bridge not found')
      }

      console.log('Zalo room bridge found:', {
        zalo_room_uuid: zaloRoom.zalo_room_uuid,
        zalo_contact_uuid: zaloRoom.zalo_contact_uuid,
        contact_display_name: zaloRoom.v_zalo_oa_contacts.display_name,
        zalo_user_id: zaloRoom.v_zalo_oa_contacts.zalo_user_id,
        is_follower: zaloRoom.v_zalo_oa_contacts.is_follower
      })

      // Create internal message first
      console.log('Creating internal message...')

      const internalMessage = await createInternalMessage(
        roomUuid,
        authorUuid,
        content,
        mapMessageType('text', 'zalo'),
        undefined,
        0 // Regular outbound message
      )

      console.log('Internal message created:', {
        message_id: internalMessage.message_id.toString(),
        room_uuid: roomUuid,
        author_uuid: authorUuid
      })

      // Send to ZALO API
      console.log('=== SENDING TO ZALO API ===')
      console.log('Contact info:', {
        zalo_user_id: zaloRoom.v_zalo_oa_contacts.zalo_user_id,
        display_name: zaloRoom.v_zalo_oa_contacts.display_name,
        is_follower: zaloRoom.v_zalo_oa_contacts.is_follower
      })
      console.log('Sending to user_id:', zaloRoom.v_zalo_oa_contacts.zalo_user_id)
      console.log('Message content:', content.trim())

      const zaloResponse = await this.sendToZaloApi(
        domainUuid,
        zaloRoom.v_zalo_oa_contacts.zalo_user_id,
        content.trim()
      )

      console.log('Zalo API response:', {
        success: zaloResponse.success,
        zalo_message_id: zaloResponse.zalo_message_id,
        error: zaloResponse.error
      })

      // Create message mapping
      console.log('Creating message mapping...')
      await this.createMessageMapping(
        domainUuid,
        internalMessage.message_id,
        {
          app_id: '',
          user_id_by_app: zaloRoom.v_zalo_oa_contacts.zalo_user_id,
          event_name: ZaloEventType.OA_SEND_TEXT,
          timestamp: Date.now(),
          sender: { user_id: 'agent' },
          recipient: { id: zaloRoom.v_zalo_oa_contacts.zalo_user_id },
          message: {
            msg_id: zaloResponse.zalo_message_id || '',
            text: content
          }
        },
        'outbound',
        zaloResponse.success ? 'sent' : 'failed',
        zaloResponse.error
      )

      console.log('Message mapping created successfully')

      // Update room last activity
      console.log('Updating room last activity...')
      await updateRoomLastActivity(roomUuid, authorUuid)

      const result = {
        internal_message_id: safeBigIntToString(internalMessage.message_id) || '0',
        room_uuid: roomUuid,
        contact_uuid: zaloRoom.zalo_contact_uuid,
        zalo_message_id: zaloResponse.zalo_message_id || '',
        delivery_status: zaloResponse.success ? 'sent' : 'failed'
      }

      console.log('=== ZALO MESSAGE SERVICE: PROCESS OUTBOUND MESSAGE SUCCESS ===')
      console.log('Final result:', result)

      return result
    } catch (error) {
      console.error('=== ZALO MESSAGE SERVICE: PROCESS OUTBOUND MESSAGE ERROR ===')
      console.error('Error processing outbound ZALO message:', {
        name: (error as Error)?.name,
        message: (error as Error)?.message,
        stack: (error as Error)?.stack,
        domainUuid,
        roomUuid,
        authorUuid
      })
      throw error
    }
  }

  /**
   * Process outbound attachment message from internal chat to ZALO
   */
  static async processOutboundAttachmentMessage(
    domainUuid: string,
    roomUuid: string,
    authorUuid: string,
    attachmentData: {
      filename: string
      content_type: string
      file_buffer: Buffer
      file_size: number
    },
    messageType: MessageType = MessageType.FILE
  ): Promise<{
    internal_message_id: string
    room_uuid: string
    contact_uuid: string
    zalo_message_id: string
    delivery_status: string
    attachment_id?: string
  } | null> {
    console.log('=== ZALO MESSAGE SERVICE: PROCESS OUTBOUND ATTACHMENT MESSAGE START ===')
    console.log('Parameters:', {
      domainUuid,
      roomUuid,
      authorUuid,
      filename: attachmentData.filename,
      content_type: attachmentData.content_type,
      file_size: attachmentData.file_size,
      messageType
    })

    try {
      // Validate attachment data
      if (!attachmentData.file_buffer || attachmentData.file_size === 0) {
        throw new Error('Invalid attachment data')
      }

      // Validate file for Zalo upload
      const validation = ZaloFileUploadService.validateFile(attachmentData.file_buffer, attachmentData.content_type)

      if (!validation.valid) {
        throw new Error(validation.error || 'File validation failed')
      }

      console.log('Attachment validation passed, upload type:', validation.uploadType)

      // Find ZALO room bridge
      console.log('Looking for Zalo room bridge...')

      const zaloRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
        where: {
          domain_uuid: domainUuid,
          internal_room_uuid: roomUuid
        },
        include: {
          v_zalo_oa_contacts: true
        }
      })

      if (!zaloRoom) {
        console.error('ZALO room bridge not found:', { domainUuid, roomUuid })
        throw new Error('ZALO room bridge not found')
      }

      console.log('Zalo room bridge found:', {
        zalo_room_uuid: zaloRoom.zalo_room_uuid,
        zalo_contact_uuid: zaloRoom.zalo_contact_uuid,
        contact_display_name: zaloRoom.v_zalo_oa_contacts.display_name,
        zalo_user_id: zaloRoom.v_zalo_oa_contacts.zalo_user_id,
        is_follower: zaloRoom.v_zalo_oa_contacts.is_follower
      })

      // Upload file to Zalo first
      console.log('=== UPLOADING FILE TO ZALO ===')

      const uploadResponse = await ZaloFileUploadService.uploadAuto(
        domainUuid,
        attachmentData.file_buffer,
        attachmentData.filename,
        attachmentData.content_type
      )

      if (uploadResponse.error !== 0 || !uploadResponse.data?.attachment_id) {
        console.error('Zalo file upload failed:', uploadResponse)
        throw new Error(uploadResponse.message || 'File upload failed')
      }

      console.log('File uploaded successfully:', {
        attachment_id: uploadResponse.data.attachment_id,
        url: uploadResponse.data.url
      })

      // Create internal message with attachment info
      console.log('Creating internal message...')

      const internalMessage = await createInternalMessage(
        roomUuid,
        authorUuid,
        `📎 ${attachmentData.filename}`, // Display filename as content
        messageType,
        undefined,
        0 // Regular outbound message
      )

      console.log('Internal message created:', {
        message_id: internalMessage.message_id.toString(),
        room_uuid: roomUuid,
        author_uuid: authorUuid
      })

      // Send attachment message to ZALO API
      console.log('=== SENDING ATTACHMENT TO ZALO API ===')

      const zaloResponse = await this.sendAttachmentToZaloApi(
        domainUuid,
        zaloRoom.v_zalo_oa_contacts.zalo_user_id,
        uploadResponse.data.attachment_id,
        validation.uploadType || 'file'
      )

      console.log('Zalo API response:', {
        success: zaloResponse.success,
        zalo_message_id: zaloResponse.zalo_message_id,
        error: zaloResponse.error
      })

      // Create message mapping for tracking
      await this.createMessageMapping(
        domainUuid,
        internalMessage.message_id,
        {
          app_id: 'internal',
          user_id_by_app: authorUuid,
          event_name: validation.uploadType === 'image' ? ZaloEventType.OA_SEND_IMAGE : ZaloEventType.OA_SEND_FILE,
          timestamp: Date.now(),
          sender: { user_id: 'agent' },
          recipient: { id: zaloRoom.v_zalo_oa_contacts.zalo_user_id },
          message: {
            msg_id: zaloResponse.zalo_message_id || '',
            attachments: [{
              type: validation.uploadType || 'file',
              payload: {
                attachment_id: uploadResponse.data.attachment_id,
                url: uploadResponse.data.url,
                name: attachmentData.filename,
                size: attachmentData.file_size
              }
            }]
          }
        },
        'outbound',
        zaloResponse.success ? 'sent' : 'failed',
        zaloResponse.error
      )

      const result = {
        internal_message_id: safeBigIntToString(internalMessage.message_id) || '0',
        room_uuid: roomUuid,
        contact_uuid: zaloRoom.zalo_contact_uuid,
        zalo_message_id: zaloResponse.zalo_message_id || '',
        delivery_status: zaloResponse.success ? 'sent' : 'failed',
        attachment_id: uploadResponse.data.attachment_id
      }

      console.log('=== ZALO ATTACHMENT MESSAGE SERVICE: SUCCESS ===')
      console.log('Final result:', result)

      return result
    } catch (error) {
      console.error('=== ZALO ATTACHMENT MESSAGE SERVICE: ERROR ===')
      console.error('Error processing ZALO attachment message:', {
        error: error instanceof Error ? error.message : error,
        domainUuid,
        roomUuid,
        authorUuid
      })
      throw error
    }
  }

  /**
   * Find or create ZALO contact
   */
  private static async findOrCreateContact(domainUuid: string, zaloUser: ZaloUser): Promise<ZaloContact> {
    try {
      // Try to find existing contact
      let contact = await prisma.v_zalo_oa_contacts.findFirst({
        where: {
          domain_uuid: domainUuid,
          zalo_user_id: zaloUser.user_id
        }
      })

      if (!contact) {
        // Create new contact
        contact = await prisma.v_zalo_oa_contacts.create({
          data: {
            domain_uuid: domainUuid,
            zalo_user_id: zaloUser.user_id,
            display_name: zaloUser.display_name || `ZALO User ${zaloUser.user_id}`,
            avatar_url: zaloUser.avatar || null,
            username: zaloUser.user_alias || null,
            is_follower: zaloUser.user_is_follower || false,
            is_active: true,
            last_interaction_date: new Date(),
            contact_info: {
              user_gender: zaloUser.user_gender,
              user_id_by_app: zaloUser.user_id_by_app
            },
            contact_metadata: {
              created_from_webhook: true
            },
            insert_date: new Date(),
            update_date: new Date()
          }
        })
      } else {
        // Update last interaction date and user info
        contact = await prisma.v_zalo_oa_contacts.update({
          where: { contact_uuid: contact.contact_uuid },
          data: {
            display_name: zaloUser.display_name || contact.display_name,
            avatar_url: zaloUser.avatar || contact.avatar_url,
            username: zaloUser.user_alias || contact.username,
            is_follower: zaloUser.user_is_follower !== undefined ? zaloUser.user_is_follower : contact.is_follower,
            is_active: true,
            last_interaction_date: new Date(),
            contact_info: {
              ...(contact.contact_info as any),
              user_gender: zaloUser.user_gender,
              user_id_by_app: zaloUser.user_id_by_app
            },
            update_date: new Date()
          }
        })
      }

      return contact as ZaloContact
    } catch (error) {
      console.error('Error finding/creating ZALO contact:', error)
      throw error
    }
  }

  /**
   * Find or create ZALO chat room bridge
   */
  private static async findOrCreateRoom(
    domainUuid: string,
    contact: ZaloContact,
    zaloUserId: string
  ): Promise<ZaloChatRoom> {
    try {
      // Try to find existing room
      let zaloRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
        where: {
          domain_uuid: domainUuid,
          zalo_contact_uuid: contact.contact_uuid
        }
      })

      if (!zaloRoom) {
        // Create internal chat room first
        const internalRoom = await createInternalChatRoom(
          domainUuid,
          `ZALO Chat - ${contact.display_name || zaloUserId}`,
          `ZALO OA conversation with ${contact.display_name || zaloUserId}`,
          'zalo',
          zaloUserId
        )

        // Create ZALO chat room bridge
        zaloRoom = await prisma.v_zalo_oa_chat_rooms.create({
          data: {
            domain_uuid: domainUuid,
            internal_room_uuid: internalRoom.room_uuid,
            zalo_contact_uuid: contact.contact_uuid,
            room_status: 'active',
            last_message_at: new Date(),
            conversation_metadata: {
              zalo_user_id: zaloUserId,
              created_from_webhook: true
            },
            insert_date: new Date(),
            update_date: new Date()
          }
        })
      } else {
        // Update last message time
        zaloRoom = await prisma.v_zalo_oa_chat_rooms.update({
          where: { zalo_room_uuid: zaloRoom.zalo_room_uuid },
          data: {
            last_message_at: new Date(),
            update_date: new Date()
          }
        })
      }

      return zaloRoom as ZaloChatRoom
    } catch (error) {
      console.error('Error finding/creating ZALO chat room:', error)
      throw error
    }
  }

  /**
   * Create message mapping for tracking
   */
  private static async createMessageMapping(
    domainUuid: string,
    internalMessageId: bigint,
    webhookEvent: ZaloWebhookPayload,
    direction: 'inbound' | 'outbound',
    deliveryStatus: string = 'pending',
    errorMessage?: string
  ): Promise<void> {
    try {
      await prisma.v_zalo_message_mapping.create({
        data: {
          domain_uuid: domainUuid,
          internal_message_id: internalMessageId,
          zalo_message_id: webhookEvent.message?.msg_id || null,
          zalo_user_id: webhookEvent.sender.user_id,
          message_direction: direction,
          zalo_event_name: webhookEvent.event_name,
          zalo_event_type: this.getMessageTypeFromEvent(webhookEvent.event_name),
          delivery_status: deliveryStatus,
          error_message: errorMessage || null,
          message_metadata: {
            timestamp: webhookEvent.timestamp,
            app_id: webhookEvent.app_id,
            user_id_by_app: webhookEvent.user_id_by_app
          },
          retry_count: 0,
          insert_date: new Date()
        }
      })
    } catch (error) {
      console.error('Error creating ZALO message mapping:', error)
      throw error
    }
  }

  /**
   * Send message to ZALO API with automatic token refresh
   */
  private static async sendToZaloApi(
    domainUuid: string,
    zaloUserId: string,
    content: string
  ): Promise<{
    success: boolean;
    zalo_message_id?: string;
    error?: string;
    quota_info?: any;
    error_type?: string;
    error_code?: number;
    original_message?: string;
  }> {
    console.log('=== ZALO API SEND MESSAGE START ===')
    console.log('Parameters:', {
      domainUuid,
      zaloUserId,
      content: content.substring(0, 50) + (content.length > 50 ? '...' : '')
    })

    try {
      // Verify configuration exists
      const config = await prisma.v_zalo_oa_config.findFirst({
        where: {
          domain_uuid: domainUuid,
          is_active: true
        },
        select: {
          oa_id: true,
          app_id: true,
          config_uuid: true
        }
      })

      if (!config) {
        console.error('No active Zalo configuration found for domain:', domainUuid)
        throw new Error('ZALO configuration not found for this domain')
      }

      console.log('Zalo configuration found:', {
        config_uuid: config.config_uuid,
        app_id: config.app_id,
        oa_id: config.oa_id
      })

      const payload = {
        recipient: {
          user_id: zaloUserId
        },
        message: {
          text: content
        }
      }

      console.log('Preparing API call to Zalo:', {
        endpoint: 'https://openapi.zalo.me/v3.0/oa/message/cs',
        user_id: zaloUserId,
        content: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
        payload
      })

      // Use token service to make API call with automatic refresh
      const response = await ZaloTokenService.makeApiCallWithTokenRefresh(
        domainUuid,
        async (accessToken: string) => {
          console.log('Making HTTP request to Zalo API with token...')

          const res = await fetch('https://openapi.zalo.me/v3.0/oa/message/cs', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              access_token: accessToken
            },
            body: JSON.stringify(payload)
          })

          console.log('Zalo API HTTP response status:', res.status, res.statusText)

          if (!res.ok) {
            const errorText = await res.text()

            console.error('ZALO API HTTP error details:', {
              status: res.status,
              statusText: res.statusText,
              errorText,
              headers: Object.fromEntries(res.headers.entries())
            })

            // Create error object that token service can recognize
            const error: any = new Error(`ZALO API HTTP error: ${res.status} ${res.statusText} - ${errorText}`)

            error.status = res.status
            throw error
          }

          const jsonResponse = await res.json()

          console.log('Zalo API JSON response:', jsonResponse)

          // Check for token-related errors in the response
          if (jsonResponse.error === -201) {
            const tokenError: any = new Error('Invalid access token')

            tokenError.error_code = -201
            tokenError.error = -201
            throw tokenError
          }

          return jsonResponse
        }
      )

      if (response.error !== 0) {
        console.error('ZALO API business error:', {
          error_code: response.error,
          error_message: response.message,
          full_response: response
        })

        // Provide user-friendly error messages based on Zalo error codes
        let userFriendlyError = response.message || `ZALO API error code: ${response.error}`
        let errorType = 'api_error'

        // Common Zalo API error codes and their meanings
        switch (response.error) {
          case -124:
            userFriendlyError = 'User has not followed the Zalo OA. Please ask the user to follow your Zalo Official Account first.'
            errorType = 'user_not_follower'
            break
          case -216:
            userFriendlyError = 'Message quota exceeded. Please try again later or upgrade your Zalo OA plan.'
            errorType = 'quota_exceeded'
            break
          case -201:
            userFriendlyError = 'Access token was refreshed automatically. Please try sending the message again.'
            errorType = 'token_refreshed'
            break
          case -200:
            userFriendlyError = 'Invalid request format. Please contact support.'
            errorType = 'invalid_request'
            break
          case -214:
            userFriendlyError = 'User has blocked the Zalo OA. Message cannot be delivered.'
            errorType = 'user_blocked'
            break
          case -215:
            userFriendlyError = 'Message content violates Zalo policies. Please modify your message.'
            errorType = 'content_violation'
            break
          default:
            if (response.error < 0) {
              userFriendlyError = `Cannot send message via Zalo. Please open the Zalo application and send the message directly. Error: ${response.message || 'Unknown error'}`
              errorType = 'send_via_app'
            }

            break
        }

        return {
          success: false,
          error: userFriendlyError,
          error_type: errorType,
          error_code: response.error,
          original_message: response.message
        }
      }

      const result = {
        success: true,
        zalo_message_id: response.data?.message_id,
        quota_info: response.data?.quota
      }

      console.log('=== ZALO API SEND MESSAGE SUCCESS ===')
      console.log('Success result:', result)

      return result
    } catch (error) {
      console.error('=== ZALO API SEND MESSAGE ERROR ===')
      console.error('Error sending message to ZALO API:', {
        name: (error as Error)?.name,
        message: (error as Error)?.message,
        stack: (error as Error)?.stack,
        domainUuid,
        zaloUserId
      })

      // Provide user-friendly error messages for network/connection errors
      let userFriendlyError = 'Unknown error occurred while sending to Zalo'
      let errorType = 'network_error'

      if (error instanceof Error) {
        if (error.message.includes('No valid access token available')) {
          userFriendlyError = 'Zalo access token is not available or could not be refreshed. Please reconfigure your Zalo integration.'
          errorType = 'token_unavailable'
        } else if (error.message.includes('fetch')) {
          userFriendlyError = 'Cannot connect to Zalo servers. Please check your internet connection and try again.'
          errorType = 'connection_error'
        } else if (error.message.includes('timeout')) {
          userFriendlyError = 'Request to Zalo timed out. Please try again later.'
          errorType = 'timeout_error'
        } else if (error.message.includes('access token')) {
          userFriendlyError = 'Zalo access token issue was handled automatically. Please try sending the message again.'
          errorType = 'token_handled'
        } else {
          userFriendlyError = `Cannot send message via Zalo. Please open the Zalo application and send the message directly. Error: ${error.message}`
          errorType = 'send_via_app'
        }
      }

      return {
        success: false,
        error: userFriendlyError,
        error_type: errorType,
        original_message: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Send attachment message to ZALO API with automatic token refresh
   */
  private static async sendAttachmentToZaloApi(
    domainUuid: string,
    zaloUserId: string,
    attachmentId: string,
    attachmentType: 'image' | 'file'
  ): Promise<{
    success: boolean;
    zalo_message_id?: string;
    error?: string;
    quota_info?: any;
    error_type?: string;
    error_code?: number;
    original_message?: string;
  }> {
    console.log('=== ZALO API SEND ATTACHMENT MESSAGE START ===')
    console.log('Parameters:', {
      domainUuid,
      zaloUserId,
      attachmentId,
      attachmentType
    })

    try {
      // Verify configuration exists
      const config = await prisma.v_zalo_oa_config.findFirst({
        where: {
          domain_uuid: domainUuid,
          is_active: true
        },
        select: {
          oa_id: true,
          app_id: true,
          config_uuid: true
        }
      })

      if (!config) {
        console.error('No active Zalo configuration found for domain:', domainUuid)
        throw new Error('ZALO configuration not found for this domain')
      }

      console.log('Zalo configuration found:', {
        config_uuid: config.config_uuid,
        app_id: config.app_id,
        oa_id: config.oa_id
      })

      const payload = {
        recipient: {
          user_id: zaloUserId
        },
        message: {
          attachment: {
            type: attachmentType,
            payload: {
              attachment_id: attachmentId
            }
          }
        }
      }

      console.log('Preparing API call to Zalo:', {
        endpoint: ZaloURL.ZALO_CS_URL,
        user_id: zaloUserId,
        attachment_type: attachmentType,
        attachment_id: attachmentId,
        payload
      })

      // Use token service to make API call with automatic refresh
      const response = await ZaloTokenService.makeApiCallWithTokenRefresh(
        domainUuid,
        async (accessToken: string) => {
          console.log('Making HTTP request to Zalo API with token...')

          const res = await fetch(ZaloURL.ZALO_CS_URL, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              access_token: accessToken
            },
            body: JSON.stringify(payload)
          })

          console.log('Zalo API HTTP response status:', res.status, res.statusText)

          if (!res.ok) {
            const errorText = await res.text()

            console.error('ZALO API HTTP error details:', {
              status: res.status,
              statusText: res.statusText,
              errorText,
              headers: Object.fromEntries(res.headers.entries())
            })

            // Create error object that token service can recognize
            const error: any = new Error(`ZALO API HTTP error: ${res.status} ${res.statusText} - ${errorText}`)

            error.status = res.status
            throw error
          }

          const jsonResponse = await res.json()

          console.log('Zalo API JSON response:', jsonResponse)

          // Check for token-related errors in the response
          if (jsonResponse.error === -201) {
            const tokenError: any = new Error('Invalid access token')

            tokenError.error_code = -201
            tokenError.error = -201
            throw tokenError
          }

          return jsonResponse
        }
      )

      if (response.error !== 0) {
        console.error('ZALO API business error:', {
          error_code: response.error,
          error_message: response.message,
          full_response: response
        })

        // Provide user-friendly error messages based on Zalo error codes
        let userFriendlyError = response.message || `ZALO API error code: ${response.error}`
        let errorType = 'api_error'

        // Common Zalo API error codes and their meanings
        switch (response.error) {
          case -124:
            userFriendlyError = 'User has not followed the Zalo OA. Please ask the user to follow your Zalo Official Account first.'
            errorType = 'user_not_follower'
            break
          case -216:
            userFriendlyError = 'Message quota exceeded. Please try again later or upgrade your Zalo OA plan.'
            errorType = 'quota_exceeded'
            break
          case -201:
            userFriendlyError = 'Access token was refreshed automatically. Please try sending the message again.'
            errorType = 'token_refreshed'
            break
          case -200:
            userFriendlyError = 'Invalid request format. Please contact support.'
            errorType = 'invalid_request'
            break
          case -214:
            userFriendlyError = 'User has blocked the Zalo OA. Message cannot be delivered.'
            errorType = 'user_blocked'
            break
          case -215:
            userFriendlyError = 'Message content violates Zalo policies. Please modify your message.'
            errorType = 'content_violation'
            break
          default:
            if (response.error < 0) {
              userFriendlyError = `Cannot send attachment via Zalo. Please open the Zalo application and send the file directly. Error: ${response.message || 'Unknown error'}`
              errorType = 'send_via_app'
            }

            break
        }

        return {
          success: false,
          error: userFriendlyError,
          error_type: errorType,
          error_code: response.error,
          original_message: response.message
        }
      }

      const result = {
        success: true,
        zalo_message_id: response.data?.message_id,
        quota_info: response.data?.quota
      }

      console.log('=== ZALO API SEND ATTACHMENT MESSAGE SUCCESS ===')
      console.log('Success result:', result)

      return result
    } catch (error) {
      console.error('=== ZALO API SEND ATTACHMENT MESSAGE ERROR ===')
      console.error('Error sending attachment message to ZALO API:', {
        error: error instanceof Error ? error.message : error,
        domainUuid,
        zaloUserId,
        attachmentId,
        attachmentType
      })

      // Handle different types of errors with user-friendly messages
      let userFriendlyError = 'Failed to send attachment via Zalo'
      let errorType = 'unknown_error'

      if (error instanceof Error) {
        if (error.message.includes('timeout') || error.message.includes('ECONNRESET')) {
          userFriendlyError = 'Connection timeout. Please check your internet connection and try again.'
          errorType = 'timeout_error'
        } else if (error.message.includes('access token')) {
          userFriendlyError = 'Zalo access token issue was handled automatically. Please try sending the attachment again.'
          errorType = 'token_handled'
        } else {
          userFriendlyError = `Cannot send attachment via Zalo. Please open the Zalo application and send the file directly. Error: ${error.message}`
          errorType = 'send_via_app'
        }
      }

      return {
        success: false,
        error: userFriendlyError,
        error_type: errorType,
        original_message: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * Get message type from ZALO event name
   */
  private static getMessageTypeFromEvent(eventName: ZaloEventType): string {
    switch (eventName) {
      case ZaloEventType.USER_SEND_TEXT:
      case ZaloEventType.OA_SEND_TEXT:
        return 'text'
      case ZaloEventType.USER_SEND_IMAGE:
      case ZaloEventType.OA_SEND_IMAGE:
        return 'image'
      case ZaloEventType.USER_SEND_FILE:
      case ZaloEventType.OA_SEND_FILE:
        return 'file'
      case ZaloEventType.USER_SEND_AUDIO:
        return 'audio'
      case ZaloEventType.USER_SEND_VIDEO:
        return 'video'
      case ZaloEventType.USER_SEND_STICKER:
        return 'sticker'
      case ZaloEventType.USER_SEND_LOCATION:
        return 'location'
      default:
        return 'text'
    }
  }
}
