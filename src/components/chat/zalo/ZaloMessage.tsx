// Zalo Message Component
// Displays messages with Zalo-specific formatting, indicators, and attachment support

'use client'

import React from 'react'

import { Check, CheckCheck, Clock, AlertCircle, Send, Download, FileText, Image as ImageIcon, Eye } from 'lucide-react'

import type { ChatMessage, MessageAttachment } from '@/types/apps/internal-chat/chatTypes'

interface ZaloMessageProps {
  message: ChatMessage
  isOwnMessage: boolean
  showAvatar?: boolean
  compact?: boolean
  className?: string
}

const ZaloMessage: React.FC<ZaloMessageProps> = ({
  message,
  isOwnMessage,
  showAvatar = true,
  compact = false,
  className = ''
}) => {
  // Format timestamp
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp * 1000)

    
return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  // Get delivery status icon for outbound messages
  const getDeliveryStatusIcon = () => {
    if (!isOwnMessage) return null

    // Check if this is a Zalo message by looking for mapping
    const deliveryStatus = (message as any).delivery_status

    switch (deliveryStatus) {
      case 'pending':
        return <Clock className='w-3 h-3 text-gray-400' />
      case 'sent':
        return <Check className='w-3 h-3 text-gray-500' />
      case 'delivered':
        return <CheckCheck className='w-3 h-3 text-green-500' />
      case 'failed':
        return <AlertCircle className='w-3 h-3 text-red-500' />
      default:
        return <Send className='w-3 h-3 text-gray-400' />
    }
  }

  // Check if message is from Zalo user
  const isZaloUser = message.author_uuid === 'zalo-user'

  // Get message content with Zalo-specific formatting
  const getFormattedContent = () => {
    if (!message.content) return ''

    // Basic formatting support
    let content = message.content

    // Convert basic markdown-like formatting
    content = content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
      .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic
      .replace(/`(.*?)`/g, '<code>$1</code>') // Code

    return content
  }

  // Handle attachment download
  const handleDownload = (attachment: MessageAttachment) => {
    if (attachment.download_url) {
      window.open(attachment.download_url, '_blank')
    }
  }

  // Handle image preview
  const handleImagePreview = (attachment: MessageAttachment) => {
    if (attachment.download_url) {
      // Open image in new tab for preview
      window.open(attachment.download_url, '_blank')
    }
  }

  // Format file size
  const formatFileSize = (bytes: number | null) => {
    if (!bytes) return 'Unknown size'
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))

    
return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
  }

  // Render attachment
  const renderAttachment = (attachment: MessageAttachment) => {
    const isImage = attachment.is_image || attachment.content_type?.startsWith('image/')
    
    if (isImage) {
      return (
        <div key={attachment.attachment_id} className="mt-2">
          <div className="relative group">
            <img
              src={attachment.download_url || attachment.file_path}
              alt={attachment.filename}
              className="max-w-xs rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
              onClick={() => handleImagePreview(attachment)}
              onError={(e) => {
                // Fallback to file icon if image fails to load
                e.currentTarget.style.display = 'none'
                e.currentTarget.nextElementSibling?.classList.remove('hidden')
              }}
            />
            <div className="hidden bg-gray-100 p-4 rounded-lg border-2 border-dashed border-gray-300">
              <div className="flex items-center space-x-3">
                <ImageIcon className="w-8 h-8 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{attachment.filename}</p>
                  <p className="text-xs text-gray-500">{formatFileSize(attachment.file_size)}</p>
                </div>
                <button
                  onClick={() => handleImagePreview(attachment)}
                  className="p-2 text-gray-500 hover:text-gray-700 rounded"
                  title="View image"
                >
                  <Eye className="w-4 h-4" />
                </button>
              </div>
            </div>
            {/* Image overlay with preview button */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
              <button
                onClick={() => handleImagePreview(attachment)}
                className="opacity-0 group-hover:opacity-100 bg-white bg-opacity-90 p-2 rounded-full transition-opacity"
                title="View full size"
              >
                <Eye className="w-4 h-4 text-gray-700" />
              </button>
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-1">{attachment.filename}</p>
        </div>
      )
    } else {
      // File attachment
      return (
        <div key={attachment.attachment_id} className="mt-2">
          <div className="bg-gray-50 p-3 rounded-lg border">
            <div className="flex items-center space-x-3">
              <FileText className="w-8 h-8 text-gray-400 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">{attachment.filename}</p>
                <p className="text-xs text-gray-500">{formatFileSize(attachment.file_size)}</p>
              </div>
              <button
                onClick={() => handleDownload(attachment)}
                className="p-2 text-gray-500 hover:text-gray-700 rounded"
                title="Download file"
              >
                <Download className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      )
    }
  }

  return (
    <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} ${className}`}>
      <div
        className={`flex ${isOwnMessage ? 'flex-row-reverse' : 'flex-row'} items-end space-x-2 max-w-xs lg:max-w-md`}
      >
        {/* Avatar */}
        {showAvatar && !compact && (
          <div className='flex-shrink-0'>
            {isZaloUser ? (
              <div className='w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center'>
                <div className='w-4 h-4 bg-blue-600 rounded-full'></div>
              </div>
            ) : (
              <div className='w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center'>
                <div className='w-6 h-6 bg-gray-300 rounded-full'></div>
              </div>
            )}
          </div>
        )}

        {/* Message bubble */}
        <div
          className={`relative px-4 py-2 rounded-lg ${
            isOwnMessage ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-900'
          } ${compact ? 'text-sm' : ''}`}
        >
          {/* Zalo platform indicator */}
          {isZaloUser && (
            <div className='flex items-center mb-1'>
              <div className='w-3 h-3 bg-blue-400 rounded-full mr-1'></div>
              <span className='text-xs text-blue-200'>Zalo</span>
            </div>
          )}

          {/* Message content */}
          {message.content && (
            <div className='break-words' dangerouslySetInnerHTML={{ __html: getFormattedContent() }} />
          )}

          {/* Attachments */}
          {message.attachments && message.attachments.length > 0 && (
            <div className="space-y-2">
              {message.attachments.map(renderAttachment)}
            </div>
          )}

          {/* Message metadata */}
          <div
            className={`flex items-center justify-end mt-1 space-x-1 ${
              isOwnMessage ? 'text-blue-100' : 'text-gray-500'
            }`}
          >
            {/* Edited indicator */}
            {message.edited_at && <span className='text-xs opacity-75'>edited</span>}

            {/* Timestamp */}
            <span className='text-xs'>{formatTime(message.created_at)}</span>

            {/* Delivery status for own messages */}
            {isOwnMessage && getDeliveryStatusIcon()}
          </div>

          {/* Message tail */}
          <div
            className={`absolute bottom-0 ${isOwnMessage ? '-right-1' : '-left-1'} w-3 h-3 ${
              isOwnMessage ? 'bg-blue-600' : 'bg-gray-100'
            } transform rotate-45`}
          ></div>
        </div>
      </div>
    </div>
  )
}

// Zalo Message List Component
interface ZaloMessageListProps {
  messages: ChatMessage[]
  currentUserId: string
  compact?: boolean
  className?: string
}

export const ZaloMessageList: React.FC<ZaloMessageListProps> = ({
  messages,
  currentUserId,
  compact = false,
  className = ''
}) => {
  return (
    <div className={`space-y-4 ${className}`}>
      {messages.map((message, index) => {
        const isOwnMessage = message.author_uuid === currentUserId
        const showAvatar = !compact && (index === 0 || messages[index - 1].author_uuid !== message.author_uuid)

        return (
          <ZaloMessage
            key={message.message_id}
            message={message}
            isOwnMessage={isOwnMessage}
            showAvatar={showAvatar}
            compact={compact}
          />
        )
      })}
    </div>
  )
}

export default ZaloMessage
