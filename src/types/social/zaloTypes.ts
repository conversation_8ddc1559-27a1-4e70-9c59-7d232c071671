// Enhanced ZALO Integration Types
// Following clean code guidelines with clear, descriptive interfaces

// ZALO API URLs
export const ZaloURL = {
  ZALO_CS_URL: 'https://openapi.zalo.me/v3.0/oa/message/cs',
  ZALO_UPLOAD_IMAGE_URL: 'https://openapi.zalo.me/v2.0/oa/upload/image',
  ZALO_UPLOAD_FILE_URL: 'https://openapi.zalo.me/v2.0/oa/upload/file'
}

export interface ZaloOaConfig {
  config_uuid: string
  domain_uuid: string
  app_id: string
  app_secret: string
  oa_id: string
  access_token: string
  refresh_token?: string
  webhook_url?: string
  webhook_secret?: string
  is_active: boolean
  token_expires_at?: Date
  allowed_events: string[]
  oa_settings: Record<string, any>
  insert_date: Date
  insert_user?: string
  update_date: Date
  update_user?: string
}

export interface ZaloOaConfigRequest {
  app_id: string
  app_secret: string
  oa_id: string
  access_token: string
  refresh_token?: string
  webhook_url?: string
  webhook_secret?: string
  token_expires_at?: string
  allowed_events?: string[]
  oa_settings?: Record<string, any>
}

export interface ZaloOaConfigResponse {
  success: boolean
  config?: Omit<ZaloOaConfig, 'app_secret' | 'access_token' | 'refresh_token'>
  token_valid?: boolean
  token_error?: string
  message: string
}

export interface ZaloContact {
  contact_uuid: string
  domain_uuid: string
  zalo_user_id: string
  display_name?: string
  avatar_url?: string
  phone?: string
  username?: string
  is_follower: boolean
  is_active: boolean
  last_interaction_date?: Date
  contact_info: Record<string, any>
  contact_metadata: Record<string, any>
  insert_date: Date
  insert_user?: string
  update_date: Date
  update_user?: string
}

export interface ZaloChatRoom {
  zalo_room_uuid: string
  domain_uuid: string
  internal_room_uuid: string
  zalo_contact_uuid: string
  assigned_agent_uuid?: string
  room_status: 'active' | 'closed' | 'transferred' | 'pending'
  last_message_at?: Date
  conversation_metadata: Record<string, any>
  insert_date: Date
  insert_user?: string
  update_date: Date
  update_user?: string
}

export interface ZaloMessageMapping {
  mapping_uuid: string
  domain_uuid: string
  internal_message_id: bigint
  zalo_message_id?: string
  zalo_user_id?: string
  message_direction: 'inbound' | 'outbound'
  zalo_event_name?: string
  zalo_event_type?: string
  delivery_status: 'pending' | 'sent' | 'delivered' | 'failed'
  error_message?: string
  message_metadata: Record<string, any>
  retry_count: number
  last_retry_at?: Date
  insert_date: Date
}

export interface ZaloWebhookEvent {
  event_id: string
  domain_uuid?: string
  zalo_userid?: string
  event_name?: string
  msg: Record<string, any>
  insert_date: Date
  insert_user?: string
  update_date: Date
  update_user?: string
}

// ZALO API Types
export interface ZaloUser {
  user_id: string
  display_name?: string
  user_alias?: string
  user_is_follower?: boolean
  avatar?: string
  user_gender?: number
  user_id_by_app?: string
}

export interface ZaloMessage {
  msg_id: string
  text?: string
  attachments?: ZaloAttachment[]
  links?: ZaloLink[]
  location?: ZaloLocation
}

export interface ZaloAttachment {
  type: 'image' | 'file' | 'audio' | 'video' | 'sticker' | 'gif'
  payload: {
    url?: string
    thumbnail?: string
    description?: string
    name?: string
    size?: number
    checksum?: string
    type?: string
    attachment_id?: string
    coordinates?: {
      latitude: number
      longitude: number
    }
  }
}

export interface ZaloLink {
  url: string
  title?: string
  description?: string
  thumb?: string
}

export interface ZaloLocation {
  latitude: number
  longitude: number
  address?: string
}

export interface ZaloWebhookPayload {
  app_id: string
  user_id_by_app: string
  event_name: ZaloEventType
  timestamp: number
  sender: ZaloUser
  recipient: {
    id: string
  }
  message?: ZaloMessage
  postback?: {
    title: string
    payload: string
  }
}

// ZALO Event Types
export enum ZaloEventType {
  USER_SEND_TEXT = 'user_send_text',
  USER_SEND_IMAGE = 'user_send_image',
  USER_SEND_FILE = 'user_send_file',
  USER_SEND_AUDIO = 'user_send_audio',
  USER_SEND_VIDEO = 'user_send_video',
  USER_SEND_STICKER = 'user_send_sticker',
  USER_SEND_GIF = 'user_send_gif',
  USER_SEND_LOCATION = 'user_send_location',
  USER_SEND_BUSINESS_CARD = 'user_send_business_card',
  USER_SUBMIT_INFO = 'user_submit_info',
  USER_CLICK_CHABOT = 'user_click_chabot',
  OA_SEND_TEXT = 'oa_send_text',
  OA_SEND_IMAGE = 'oa_send_image',
  OA_SEND_FILE = 'oa_send_file',
  FOLLOW = 'follow',
  UNFOLLOW = 'unfollow'
}

export enum ZaloMessageType {
  TEXT = 0,
  IMAGE = 1,
  FILE = 2,
  SYSTEM = 3,
  AUDIO = 4,
  VIDEO = 5,
  STICKER = 6,
  LOCATION = 7
}

// ZALO API Response Types
export interface ZaloApiResponse<T = any> {
  error: number
  message: string
  data?: T
}

export interface ZaloOaInfo {
  oa_id: string
  name: string
  description?: string
  avatar?: string
  cover?: string
  is_verified?: boolean
  cate_name?: string
  num_follower?: number
  oa_alias?: string
  is_official?: boolean
  oa_type?: number
  package_name?: string
  package_valid_through_date?: string
  package_auto_renew_date?: string
  linked_zca?: string
}

export interface ZaloSendMessageRequest {
  room_uuid: string
  content: string
  message_type?: 'text' | 'image' | 'file' | 'template'
  template_data?: Record<string, any>
  quick_replies?: Array<{
    type: 'text'
    title: string
    payload: string
  }>
}

export interface ZaloSendMessageResponse {
  success: boolean
  zalo_message_id?: string
  error?: string
  internal_message_id?: string
  quota_info?: {
    daily_quota: number
    remaining_quota: number
  }
}

// ZALO File Upload Types
export interface ZaloUploadImageRequest {
  file: File | Buffer
}

export interface ZaloUploadFileRequest {
  file: File | Buffer
}

export interface ZaloUploadResponse {
  error: number
  message: string
  data?: {
    attachment_id: string
    url?: string
    thumbnail?: string
  }
}

export interface ZaloAttachmentMessage {
  recipient: {
    user_id: string
  }
  message: {
    attachment: {
      type: 'image' | 'file'
      payload: {
        attachment_id: string
      }
    }
  }
}

export interface ZaloImageMessage {
  recipient: {
    user_id: string
  }
  message: {
    attachment: {
      type: 'image'
      payload: {
        url?: string
        attachment_id?: string
      }
    }
  }
}

export interface ZaloFileMessage {
  recipient: {
    user_id: string
  }
  message: {
    attachment: {
      type: 'file'
      payload: {
        attachment_id: string
      }
    }
  }
}

// Message Processing Types
export interface ZaloProcessedMessage {
  internal_message_id: string
  room_uuid: string
  contact_uuid: string
  zalo_message_id: string
  delivery_status: string
}

// Token Management Types
export interface ZaloTokenInfo {
  access_token: string
  refresh_token?: string
  expires_in: number
  expires_at: Date
}

export interface ZaloTokenRefreshRequest {
  app_id: string
  refresh_token: string
  app_secret: string
}

export interface ZaloTokenRefreshResponse {
  access_token: string
  refresh_token: string
  expires_in: number
}

// Configuration validation types
export interface ZaloConfigValidation {
  valid: boolean
  errors: string[]
  warnings: string[]
}

export interface ZaloTokenValidation {
  valid: boolean
  error?: string
  oaInfo?: ZaloOaInfo
  expiresAt?: Date
}

// Webhook verification types
export interface ZaloWebhookSignature {
  signature: string
  body: string
  timestamp: number
}

// UI Component Props Types
export interface ZaloConfigFormProps {
  initialConfig?: Partial<ZaloOaConfigRequest>
  onSave: (config: ZaloOaConfigRequest) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

export interface ZaloConversationListProps {
  conversations: Array<{
    room_uuid: string
    contact_name: string
    last_message: string
    last_message_time: Date
    unread_count: number
    contact_avatar?: string
    is_follower: boolean
  }>
  onSelectConversation: (roomUuid: string) => void
  selectedRoomUuid?: string
}

export interface ZaloMessageItemProps {
  message: {
    message_id: string
    content: string
    author_type: 'agent' | 'customer'
    created_at: Date
    delivery_status?: string
    attachments?: Array<{
      type: string
      url: string
      filename: string
      thumbnail?: string
    }>
  }
  showDeliveryStatus?: boolean
}

// Internal Chat Integration Types (following Telegram pattern)
export interface ZaloRoomInfo {
  zalo_room_uuid: string
  internal_room_uuid: string
  assigned_agent_uuid?: string | null
  assigned_agent?: {
    user_uuid: string
    username: string
    display_name: string
    first_name: string | null
    last_name: string | null
  } | null
  contact: {
    contact_uuid: string
    zalo_user_id: string
    display_name: string
    username: string | null
    avatar_url: string | null
    phone: string | null
    is_follower: boolean | null
    last_interaction_date: Date | null
  }
  room_status: string
  last_message_at: Date | null
  conversation_metadata: any
  participants?: Array<{
    user_uuid: string
    username: string
    user_email: string
    participant_role: string
    joined_date: Date | null
  }>
  is_assigned?: boolean
}

export interface ZaloContactWithRoom {
  contact_uuid: string
  zalo_user_id: string
  display_name: string
  username: string | null
  avatar_url: string | null
  phone: string | null
  is_follower: boolean | null
  last_interaction_date: Date | null
  contact_info: any
  chat_room: {
    zalo_room_uuid: string
    internal_room_uuid: string
    room_status: string
    last_message_at: Date | null
  } | null
}

export interface ZaloStats {
  total_contacts: number
  active_rooms: number
  unassigned_rooms: number
  total_messages_sent: number
  total_messages_received: number
  failed_messages: number
  last_activity: Date | null
}

export interface ZaloAssignmentResult {
  success: boolean
  assigned_agent_uuid: string | null
  assignment_method: 'manual' | 'none'
  message: string
}

// API Response Types for Internal Chat Integration
export interface ZaloContactsResponse {
  contacts: ZaloContactWithRoom[]
  pagination: {
    limit: number
    offset: number
    has_more: boolean
  }
}

export interface ZaloRoomsResponse {
  rooms: ZaloRoomInfo[]
  total: number
}

export interface ZaloStatsResponse {
  stats: ZaloStats
  configured: boolean
  integration_active: boolean
  token_expired?: boolean
}

export interface ZaloAssignmentResponse {
  success: boolean
  action: 'assign' | 'unassign'
  agent_uuid: string
  zalo_room_uuid: string
  message: string
}

export interface ZaloAssignmentInfoResponse {
  zalo_room_uuid: string
  assignment_status: {
    is_assigned: boolean
    agent_count: number
    agents: Array<{
      user_uuid: string
      username: string
      user_email: string
      participant_role: string
      joined_date: Date | null
    }>
  }
  available_agents: Array<{
    user_uuid: string
    username: string
    user_email: string
  }>
}

// Legacy compatibility types (for migration)
export interface ZaloOAuthLegacy {
  oauth_uuid: string
  domain_uuid: string
  app_id: string
  app_secret: string
  code_verifier?: string
  oauth_code?: string
  access_token?: string
  refresh_token?: string
  expires_at?: Date
  insert_date: Date
  insert_user?: string
  update_date: Date
  update_user?: string
}
