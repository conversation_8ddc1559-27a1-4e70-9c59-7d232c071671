/* eslint-disable */
// ZALO OA Message Sending API
// Handles outbound messages from internal chat to ZALO OA with domain isolation

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'

import { prisma } from '@/libs/db/prisma'
import { authOptions } from '@/libs/auth'
import { ZaloTokenService } from '@/services/zalo/zaloTokenService'
import type { ZaloOaMessageSendRequest, ZaloOaMessageSendResponse } from '@/types/apps/zalo-oa/zaloOaTypes'
import { ZaloURL } from '@/types/apps/zaloTypes'

// Send message to ZALO OA API
async function sendToZaloApi(
  access_token: string,
  zalo_user_id: string,
  message_content: string,
  message_type: string = 'text',
  attachment_id?: string
): Promise<ZaloOaMessageSendResponse> {
  try {
    let payload: any = {
      recipient: {
        user_id: zalo_user_id
      }
    }

    // Build message payload based on type
    if (message_type === 'text') {
      payload.message = {
        text: message_content
      }
    } else if (message_type === 'image' && attachment_id) {
      payload.message = {
        attachment: {
          type: 'image',
          payload: {
            attachment_id: attachment_id
          }
        }
      }
    } else if (message_type === 'file' && attachment_id) {
      payload.message = {
        attachment: {
          type: 'file',
          payload: {
            attachment_id: attachment_id
          }
        }
      }
    } else {
      // Fallback to text message
      payload.message = {
        text: message_content
      }
    }

    const response = await fetch(ZaloURL.ZALO_CS_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        access_token: access_token
      },
      body: JSON.stringify(payload)
    })

    if (!response.ok) {
      const error: any = new Error(`ZALO API error: ${response.status} ${response.statusText}`)
      error.status = response.status
      throw error
    }

    const result = await response.json()

    if (result.error !== 0) {
      // Check for token-related errors
      if (result.error === -201) {
        const tokenError: any = new Error('Invalid access token')
        tokenError.error_code = -201
        tokenError.error = -201
        throw tokenError
      }

      return {
        success: false,
        error: result.message || 'ZALO API error'
      }
    }

    return {
      success: true,
      zalo_message_id: result.data?.message_id,
      quota_info: result.data?.quota
    }
  } catch (error) {
    console.error('ZALO API send error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// POST /api/zalo-oa/messages/send - Send message to ZALO user
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      return NextResponse.json({ error: 'User domain not found' }, { status: 403 })
    }

    const domain_uuid = user.domain_uuid
    const body: ZaloOaMessageSendRequest = await request.json()

    // Validate required fields
    if (!body.zalo_user_id || !body.message_content) {
      return NextResponse.json(
        {
          error: 'ZALO user ID and message content are required'
        },
        { status: 400 }
      )
    }

    // Verify ZALO configuration exists for this domain
    const zaloConfig = await prisma.v_zalo_oa_config.findFirst({
      where: {
        domain_uuid,
        is_active: true
      },
      select: { config_uuid: true, app_id: true }
    })

    if (!zaloConfig) {
      return NextResponse.json(
        {
          error: 'ZALO configuration not found for this domain'
        },
        { status: 400 }
      )
    }

    // Find ZALO contact in this domain
    const zaloContact = await prisma.v_zalo_oa_contacts.findFirst({
      where: {
        domain_uuid,
        zalo_user_id: body.zalo_user_id
      }
    })

    if (!zaloContact) {
      return NextResponse.json(
        {
          error: 'ZALO contact not found in this domain'
        },
        { status: 404 }
      )
    }

    // Find or get the chat room for this contact
    const zaloChatRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
      where: {
        domain_uuid,
        zalo_contact_uuid: zaloContact.contact_uuid
      },
      include: {
        v_chat_rooms: true
      }
    })

    if (!zaloChatRoom) {
      return NextResponse.json(
        {
          error: 'Chat room not found for this contact'
        },
        { status: 404 }
      )
    }

    // Create internal message first (if internal_message_id not provided)
    let internal_message_id = body.internal_message_id

    if (!internal_message_id) {
      const internalMessage = await prisma.v_chat_messages.create({
        data: {
          room_uuid: zaloChatRoom.internal_room_uuid,
          author_uuid: session.user.id,
          content: body.message_content,
          message_type: body.message_type === 'image' ? 1 : body.message_type === 'file' ? 2 : 0,
          created_at: Math.floor(Date.now() / 1000),
          flags: 0 // Regular outbound message
        }
      })
      internal_message_id = internalMessage.message_id
    }

    // Send message to ZALO API with automatic token refresh
    const zaloResult = await ZaloTokenService.makeApiCallWithTokenRefresh(
      domain_uuid,
      async (accessToken: string) => {
        return await sendToZaloApi(
          accessToken,
          body.zalo_user_id,
          body.message_content,
          body.message_type || 'text',
          body.attachment_id
        )
      }
    )

    // Create message mapping
    const messageMapping = await prisma.v_zalo_message_mapping.create({
      data: {
        domain_uuid,
        internal_message_id,
        zalo_message_id: zaloResult.zalo_message_id,
        zalo_user_id: body.zalo_user_id,
        message_direction: 'outbound',
        delivery_status: zaloResult.success ? 'sent' : 'failed',
        error_message: zaloResult.error
      }
    })

    // Update chat room last message time
    await prisma.v_zalo_oa_chat_rooms.update({
      where: { zalo_room_uuid: zaloChatRoom.zalo_room_uuid },
      data: { last_message_at: new Date() }
    })

    // Broadcast message delivery status via Socket.IO
    try {
      if ((global as any).socketBroadcast?.broadcastMessage) {
        const room = `${domain_uuid}_zalo_chat`
        ;(global as any).socketBroadcast.broadcastMessage(
          room,
          {
            type: 'zalo_message_delivered',
            data: {
              internal_message_id,
              zalo_message_id: zaloResult.zalo_message_id,
              delivery_status: zaloResult.success ? 'sent' : 'failed',
              error: zaloResult.error
            }
          },
          domain_uuid
        )
      }
    } catch (broadcastError) {
      console.error('Socket broadcast error:', broadcastError)
      // Don't fail the API if broadcast fails
    }

    const response: ZaloOaMessageSendResponse = {
      success: zaloResult.success,
      zalo_message_id: zaloResult.zalo_message_id,
      error: zaloResult.error,
      quota_info: zaloResult.quota_info
    }

    return NextResponse.json({
      ...response,
      internal_message_id,
      mapping_uuid: messageMapping.mapping_uuid
    })
  } catch (error) {
    console.error('Error sending ZALO OA message:', error)
    return NextResponse.json(
      {
        error: 'Failed to send message'
      },
      { status: 500 }
    )
  }
}
