// Internal Chat - ZALO Send Message API
// POST /api/internal-chat/zalo/send-message - Send message to ZALO user

import type { NextRequest } from 'next/server'
import { NextResponse } from 'next/server'

import { getServerSession } from 'next-auth'

import { authOptions } from '@/libs/auth'
import { prisma } from '@/libs/db/prisma'
import { MessageType } from '@/types/apps/internal-chat/chatTypes'
import { ZaloMessageService } from '@/services/zalo/zaloMessageService'

interface SendZaloMessageRequest {
  room_uuid: string
  content: string
  message_type?: MessageType
  reply_to?: string
  attachment?: {
    filename: string
    content_type: string
    file_data: string // base64 encoded file data
    file_size: number
  }
}

// POST /api/internal-chat/zalo/send-message
export async function POST(request: NextRequest) {
  console.log('=== INTERNAL CHAT ZALO SEND MESSAGE START ===')

  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      console.error('Unauthorized: No session or user ID')

      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    console.log('User authenticated:', session.user.id)

    // Get user's domain
    const user = await prisma.v_users.findUnique({
      where: { user_uuid: session.user.id },
      select: { domain_uuid: true }
    })

    if (!user?.domain_uuid) {
      console.error('User domain not found for user:', session.user.id)

      return NextResponse.json({ error: 'User domain not found' }, { status: 404 })
    }

    console.log('User domain:', user.domain_uuid)

    const body: SendZaloMessageRequest = await request.json()
    const { room_uuid, content, message_type = MessageType.TEXT } = body

    console.log('Request body:', { room_uuid, content: content?.substring(0, 50) + '...' })

    // Validate required fields
    if (!room_uuid || !content?.trim()) {
      console.error('Missing required fields:', { room_uuid: !!room_uuid, content: !!content?.trim() })

      return NextResponse.json({ error: 'Room UUID and content are required' }, { status: 400 })
    }

    // Verify the room exists and belongs to this domain
    const room = await prisma.v_chat_rooms.findFirst({
      where: {
        room_uuid,
        domain_uuid: user.domain_uuid
      }
    })

    if (!room) {
      console.error('Room not found:', { room_uuid, domain_uuid: user.domain_uuid })

      return NextResponse.json({ error: 'Room not found' }, { status: 404 })
    }

    console.log('Room found:', room.room_uuid)

    // Verify user is a participant in this room
    const participant = await prisma.v_chat_room_participants.findFirst({
      where: {
        room_uuid,
        user_uuid: session.user.id,
        deleted_at: null
      }
    })

    if (!participant) {
      console.error('User not a participant in room:', { room_uuid, user_uuid: session.user.id })

      return NextResponse.json({ error: 'You are not a participant in this room' }, { status: 403 })
    }

    console.log('User is participant in room')

    // Check if this is actually a Zalo room
    const zaloRoom = await prisma.v_zalo_oa_chat_rooms.findFirst({
      where: {
        domain_uuid: user.domain_uuid,
        internal_room_uuid: room_uuid
      },
      include: {
        v_zalo_oa_contacts: true
      }
    })

    if (!zaloRoom) {
      console.error('This is not a Zalo room:', { room_uuid, domain_uuid: user.domain_uuid })

      return NextResponse.json({ error: 'This is not a Zalo room' }, { status: 400 })
    }

    console.log('Zalo room found:', {
      zalo_room_uuid: zaloRoom.zalo_room_uuid,
      zalo_user_id: zaloRoom.v_zalo_oa_contacts.zalo_user_id,
      contact_name: zaloRoom.v_zalo_oa_contacts.display_name
    })

    // Check Zalo configuration
    const zaloConfig = await prisma.v_zalo_oa_config.findFirst({
      where: {
        domain_uuid: user.domain_uuid,
        is_active: true
      },
      select: {
        config_uuid: true,
        app_id: true,
        oa_id: true,
        access_token: true,
        is_active: true
      }
    })

    if (!zaloConfig?.access_token) {
      console.error('Zalo configuration not found or access token missing:', {
        domain_uuid: user.domain_uuid,
        has_config: !!zaloConfig,
        has_token: !!zaloConfig?.access_token
      })

      return NextResponse.json({ error: 'Zalo integration not configured or access token missing' }, { status: 400 })
    }

    console.log('Zalo configuration found:', {
      config_uuid: zaloConfig.config_uuid,
      app_id: zaloConfig.app_id,
      oa_id: zaloConfig.oa_id,
      has_token: !!zaloConfig.access_token
    })

    // Check if this is an attachment message
    let result

    if (body.attachment && (message_type === MessageType.IMAGE || message_type === MessageType.FILE)) {
      console.log('Processing attachment message...')

      // Decode base64 file data
      const fileBuffer = Buffer.from(body.attachment.file_data, 'base64')

      console.log('Attachment details:', {
        filename: body.attachment.filename,
        content_type: body.attachment.content_type,
        file_size: body.attachment.file_size,
        buffer_size: fileBuffer.length
      })

      // Send attachment message using the ZALO message service
      console.log('Calling ZaloMessageService.processOutboundAttachmentMessage...')

      result = await ZaloMessageService.processOutboundAttachmentMessage(
        user.domain_uuid,
        room_uuid,
        session.user.id,
        {
          filename: body.attachment.filename,
          content_type: body.attachment.content_type,
          file_buffer: fileBuffer,
          file_size: body.attachment.file_size
        },
        message_type
      )
    } else {
      // Send regular text message using the ZALO message service
      console.log('Calling ZaloMessageService.processOutboundMessage...')

      result = await ZaloMessageService.processOutboundMessage(
        user.domain_uuid,
        room_uuid,
        session.user.id,
        content.trim()
      )
    }

    if (!result) {
      console.error('ZaloMessageService returned null result')

      return NextResponse.json({ error: 'Failed to send message - service returned null' }, { status: 500 })
    }

    console.log('ZaloMessageService result:', {
      internal_message_id: result.internal_message_id,
      zalo_message_id: result.zalo_message_id,
      delivery_status: result.delivery_status
    })

    // Broadcast message using Socket.IO if available
    try {
      if ((global as any).socketBroadcast?.broadcastMessage) {
        const room_name = `${user.domain_uuid}_room_${room_uuid}`

        console.log('Broadcasting message via Socket.IO:', {
          room_name,
          message_id: result.internal_message_id,
          zalo_message_id: result.zalo_message_id,
          delivery_status: result.delivery_status
        })

        ;(global as any).socketBroadcast.broadcastMessage(
          room_name,
          {
            type: 'message',
            data: {
              message_id: result.internal_message_id,
              room_uuid,
              content: content.trim(),
              author_uuid: session.user.id,
              message_type: 0, // TEXT
              created_at: Math.floor(Date.now() / 1000),
              flags: 0,
              zalo_message_id: result.zalo_message_id,
              delivery_status: result.delivery_status,
              platform: 'zalo'
            }
          },
          user.domain_uuid
        )

        // Also broadcast to Zalo-specific room for real-time updates
        const zalo_room_name = `${user.domain_uuid}_zalo_chat`

        ;(global as any).socketBroadcast.broadcastMessage(
          zalo_room_name,
          {
            type: 'zalo_message_sent',
            data: {
              room_uuid,
              message_id: result.internal_message_id,
              zalo_message_id: result.zalo_message_id,
              content: content.trim(),
              sender_uuid: session.user.id,
              delivery_status: result.delivery_status,
              timestamp: new Date().toISOString()
            }
          },
          user.domain_uuid
        )
      } else {
        console.warn('Socket.IO broadcast not available')
      }
    } catch (broadcastError) {
      console.error('Socket broadcast error:', broadcastError)

      // Don't fail the API call if broadcast fails
    }

    const response = {
      success: result.delivery_status === 'sent',
      message_id: result.internal_message_id,
      zalo_message_id: result.zalo_message_id,
      delivery_status: result.delivery_status,
      message: result.delivery_status === 'sent'
        ? 'Message sent successfully to Zalo'
        : 'Message saved to internal chat but failed to deliver to Zalo',
      user_action: result.delivery_status === 'failed'
        ? 'Please open the Zalo application and send the message directly to the user.'
        : null
    }

    console.log('=== INTERNAL CHAT ZALO SEND MESSAGE SUCCESS ===')
    console.log('Response:', response)

    // Return different status codes based on delivery status
    const statusCode = result.delivery_status === 'sent' ? 200 : 207 // 207 = Multi-Status (partial success)

    return NextResponse.json(response, { status: statusCode })
  } catch (error) {
    console.error('=== INTERNAL CHAT ZALO SEND MESSAGE ERROR ===')
    console.error('Error details:', {
      name: (error as Error)?.name,
      message: (error as Error)?.message,
      stack: (error as Error)?.stack
    })

    // Provide more specific error messages
    let errorMessage = 'Failed to send message'
    let statusCode = 500
    let errorType = 'unknown_error'
    let userAction = null

    if (error instanceof Error) {
      if (error.message.includes('ZALO room bridge not found')) {
        errorMessage = 'This room is not connected to a Zalo contact'
        statusCode = 400
        errorType = 'room_not_found'
      } else if (error.message.includes('access token')) {
        errorMessage = 'Zalo integration not properly configured - missing access token'
        statusCode = 400
        errorType = 'invalid_token'
        userAction = 'Please reconfigure your Zalo integration in the settings.'
      } else if (error.message.includes('Cannot send message via Zalo')) {
        errorMessage = error.message
        statusCode = 502
        errorType = 'send_via_app'
        userAction = 'Please open the Zalo application on your device and send the message directly to the user.'
      } else if (error.message.includes('User has not followed')) {
        errorMessage = error.message
        statusCode = 400
        errorType = 'user_not_follower'
        userAction = 'Ask the user to follow your Zalo Official Account first.'
      } else if (error.message.includes('quota exceeded')) {
        errorMessage = error.message
        statusCode = 429
        errorType = 'quota_exceeded'
        userAction = 'Please try again later or upgrade your Zalo OA plan.'
      } else if (error.message.includes('User has blocked')) {
        errorMessage = error.message
        statusCode = 400
        errorType = 'user_blocked'
        userAction = 'The user has blocked your Zalo OA. Message cannot be delivered.'
      } else if (error.message.includes('ZALO API error')) {
        errorMessage = `Zalo API error: ${error.message}`
        statusCode = 502
        errorType = 'api_error'
        userAction = 'Please try again later or contact support if the issue persists.'
      } else {
        errorMessage = error.message
        errorType = 'general_error'
        userAction = 'Please try again or contact support if the issue persists.'
      }
    }

    return NextResponse.json({
      error: errorMessage,
      error_type: errorType,
      user_action: userAction,
      details: process.env.NODE_ENV === 'development' ? (error as Error)?.message : undefined
    }, { status: statusCode })
  }
}
